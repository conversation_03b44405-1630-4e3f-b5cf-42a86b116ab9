.previewContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.previewHeader h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.preview {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.preview.light {
  background-color: #ffffff;
  color: #24292f;
}

.preview.dark {
  background-color: #0d1117;
  color: #f0f6fc;
}

/* Markdown content styling */
.preview h1,
.preview h2,
.preview h3,
.preview h4,
.preview h5,
.preview h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.25;
}

.preview h1 {
  font-size: 2rem;
  border-bottom: 1px solid;
  padding-bottom: 0.3rem;
}

.preview.light h1 {
  border-bottom-color: #d0d7de;
}

.preview.dark h1 {
  border-bottom-color: #30363d;
}

.preview h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid;
  padding-bottom: 0.3rem;
}

.preview.light h2 {
  border-bottom-color: #d0d7de;
}

.preview.dark h2 {
  border-bottom-color: #30363d;
}

.preview h3 {
  font-size: 1.25rem;
}

.preview h4 {
  font-size: 1rem;
}

.preview h5 {
  font-size: 0.875rem;
}

.preview h6 {
  font-size: 0.85rem;
}

.preview p {
  margin-bottom: 1rem;
  line-height: 1.6;
}

.preview ul,
.preview ol {
  margin-bottom: 1rem;
  padding-left: 2rem;
}

.preview li {
  margin-bottom: 0.25rem;
}

.preview blockquote {
  margin: 1rem 0;
  padding: 0 1rem;
  border-left: 4px solid;
}

.preview.light blockquote {
  border-left-color: #d0d7de;
  background-color: #f6f8fa;
}

.preview.dark blockquote {
  border-left-color: #30363d;
  background-color: #161b22;
}

.preview code {
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  border-radius: 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
}

.preview.light code {
  background-color: rgba(175, 184, 193, 0.2);
}

.preview.dark code {
  background-color: rgba(110, 118, 129, 0.4);
}

.preview pre {
  margin-bottom: 1rem;
  overflow-x: auto;
  border-radius: 6px;
}

.preview pre code {
  padding: 0;
  margin: 0;
  background: transparent;
}

.preview table {
  border-collapse: collapse;
  margin-bottom: 1rem;
  width: 100%;
}

.preview th,
.preview td {
  padding: 6px 13px;
  border: 1px solid;
}

.preview.light th,
.preview.light td {
  border-color: #d0d7de;
}

.preview.dark th,
.preview.dark td {
  border-color: #30363d;
}

.preview th {
  font-weight: 600;
}

.preview.light th {
  background-color: #f6f8fa;
}

.preview.dark th {
  background-color: #161b22;
}

.preview img {
  max-width: 100%;
  height: auto;
}

.preview a {
  color: #0969da;
  text-decoration: none;
}

.preview.dark a {
  color: #58a6ff;
}

.preview a:hover {
  text-decoration: underline;
}

/* Scrollbar styling */
.preview::-webkit-scrollbar {
  width: 8px;
}

.preview.light::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.preview.dark::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.preview.light::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.preview.dark::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.preview.light::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.preview.dark::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .preview {
    padding: 0.75rem;
  }
  
  .previewHeader {
    padding: 0.5rem 0.75rem;
  }
  
  .previewHeader h3 {
    font-size: 0.9rem;
  }
  
  .preview h1 {
    font-size: 1.75rem;
  }
  
  .preview h2 {
    font-size: 1.25rem;
  }
  
  .preview h3 {
    font-size: 1.1rem;
  }
}
