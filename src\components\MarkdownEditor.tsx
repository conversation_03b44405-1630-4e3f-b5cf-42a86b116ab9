import React from 'react'
import styles from '../styles/MarkdownEditor.module.css'

interface MarkdownEditorProps {
  value: string
  onChange: (value: string) => void
  isDarkTheme: boolean
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({ 
  value, 
  onChange, 
  isDarkTheme 
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value)
  }

  return (
    <div className={styles.editorContainer}>
      <div className={styles.editorHeader}>
        <h3>📝 Editor</h3>
        <span className={styles.charCount}>
          {value.length} characters
        </span>
      </div>
      <textarea
        className={`${styles.editor} ${isDarkTheme ? styles.dark : styles.light}`}
        value={value}
        onChange={handleChange}
        placeholder="Type your markdown here..."
        spellCheck={false}
      />
    </div>
  )
}

export default MarkdownEditor
