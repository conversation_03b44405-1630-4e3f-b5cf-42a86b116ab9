import React from 'react'
import ReactMarkdown from 'react-markdown'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { tomorrow, prism } from 'react-syntax-highlighter/dist/esm/styles/prism'
import remarkGfm from 'remark-gfm'
import styles from '../styles/MarkdownPreview.module.css'

interface MarkdownPreviewProps {
  content: string
  isDarkTheme: boolean
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ 
  content, 
  isDarkTheme 
}) => {
  return (
    <div className={styles.previewContainer}>
      <div className={styles.previewHeader}>
        <h3>👁️ Preview</h3>
      </div>
      <div className={`${styles.preview} ${isDarkTheme ? styles.dark : styles.light}`}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '')
              return !inline && match ? (
                <SyntaxHighlighter
                  style={isDarkTheme ? tomorrow : prism}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              )
            }
          }}
        >
          {content || '*Nothing to preview yet. Start typing in the editor!*'}
        </ReactMarkdown>
      </div>
    </div>
  )
}

export default MarkdownPreview
