import React, { useState, useEffect } from 'react'
import MarkdownEditor from './MarkdownEditor'
import MarkdownPreview from './MarkdownPreview'
import styles from '../styles/App.module.css'

const STORAGE_KEY = 'markdown-content'

const defaultMarkdown = `# Welcome to Markdown Viewer

This is a **live markdown editor** with real-time preview.

## Features

- ✅ Real-time preview
- ✅ Syntax highlighting for code blocks
- ✅ GitHub Flavored Markdown support
- ✅ Local storage (your content is saved automatically)
- ✅ Responsive design

## Code Example

\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
}

greet('World');
\`\`\`

## Table Example

| Feature | Status |
|---------|--------|
| Markdown parsing | ✅ |
| Code highlighting | ✅ |
| Tables | ✅ |

> **Tip**: Start typing in the left panel to see the magic happen!
`

function App() {
  const [markdown, setMarkdown] = useState('')
  const [isDarkTheme, setIsDarkTheme] = useState(false)

  // Load saved content on mount
  useEffect(() => {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      setMarkdown(saved)
    } else {
      setMarkdown(defaultMarkdown)
    }
  }, [])

  // Save content to localStorage
  useEffect(() => {
    if (markdown) {
      localStorage.setItem(STORAGE_KEY, markdown)
    }
  }, [markdown])

  const handleClear = () => {
    setMarkdown('')
    localStorage.removeItem(STORAGE_KEY)
  }

  const handleLoadExample = () => {
    setMarkdown(defaultMarkdown)
  }

  const toggleTheme = () => {
    setIsDarkTheme(!isDarkTheme)
  }

  return (
    <div className={`${styles.app} ${isDarkTheme ? styles.dark : styles.light}`}>
      <header className={styles.header}>
        <h1>Markdown Viewer</h1>
        <div className={styles.toolbar}>
          <button onClick={handleLoadExample} className={styles.button}>
            Load Example
          </button>
          <button onClick={handleClear} className={styles.button}>
            Clear
          </button>
          <button onClick={toggleTheme} className={styles.button}>
            {isDarkTheme ? '☀️' : '🌙'}
          </button>
        </div>
      </header>
      
      <main className={styles.main}>
        <div className={styles.editorPanel}>
          <MarkdownEditor 
            value={markdown} 
            onChange={setMarkdown}
            isDarkTheme={isDarkTheme}
          />
        </div>
        <div className={styles.previewPanel}>
          <MarkdownPreview 
            content={markdown}
            isDarkTheme={isDarkTheme}
          />
        </div>
      </main>
    </div>
  )
}

export default App
