# Markdown Viewer

A modern, real-time Markdown editor and preview application built with React, TypeScript, and Vite.

## Features

- ✅ **Real-time preview**: See your Markdown rendered as you type
- ✅ **Syntax highlighting**: Code blocks with syntax highlighting for multiple languages
- ✅ **GitHub Flavored Markdown**: Support for tables, task lists, and more
- ✅ **Dark/Light theme**: Toggle between themes
- ✅ **Local storage**: Your content is automatically saved
- ✅ **Responsive design**: Works on desktop and mobile
- ✅ **Character counter**: Track your document length
- ✅ **Quick actions**: Clear content or load example

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Usage

1. **Writing**: Type or paste your Markdown content in the left panel
2. **Preview**: See the rendered output in the right panel
3. **Theme**: Click the theme toggle button (🌙/☀️) to switch themes
4. **Clear**: Use the "Clear" button to remove all content
5. **Example**: Click "Load Example" to see sample Markdown content

## Supported Markdown Features

- Headers (H1-H6)
- **Bold** and *italic* text
- Lists (ordered and unordered)
- Links and images
- Code blocks with syntax highlighting
- Tables
- Blockquotes
- Task lists
- Strikethrough text
- And more!

## Technology Stack

- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **react-markdown** - Markdown parsing and rendering
- **react-syntax-highlighter** - Code syntax highlighting
- **remark-gfm** - GitHub Flavored Markdown support

## License

This project is open source and available under the MIT License.
