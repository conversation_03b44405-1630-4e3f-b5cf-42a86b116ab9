.editorContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  flex-shrink: 0;
}

.editorHeader h3 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.charCount {
  font-size: 0.75rem;
  color: #6c757d;
}

.editor {
  flex: 1;
  padding: 1rem;
  border: none;
  outline: none;
  resize: none;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  font-size: 14px;
  line-height: 1.5;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.editor.light {
  background-color: #ffffff;
  color: #24292f;
}

.editor.dark {
  background-color: #0d1117;
  color: #f0f6fc;
}

.editor::placeholder {
  color: #8c959f;
}

.editor:focus {
  outline: none;
}

/* Scrollbar styling */
.editor::-webkit-scrollbar {
  width: 8px;
}

.editor.light::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.editor.dark::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.editor.light::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.editor.dark::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.editor.light::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.editor.dark::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .editor {
    font-size: 16px; /* Prevent zoom on iOS */
    padding: 0.75rem;
  }
  
  .editorHeader {
    padding: 0.5rem 0.75rem;
  }
  
  .editorHeader h3 {
    font-size: 0.9rem;
  }
  
  .charCount {
    font-size: 0.7rem;
  }
}
