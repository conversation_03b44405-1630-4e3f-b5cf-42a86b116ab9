/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.app.light {
  background-color: #ffffff;
  color: #333333;
}

.app.dark {
  background-color: #1a1a1a;
  color: #e0e0e0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  border-bottom: 1px solid;
  flex-shrink: 0;
}

.light .header {
  border-bottom-color: #e0e0e0;
  background-color: #f8f9fa;
}

.dark .header {
  border-bottom-color: #333333;
  background-color: #2d2d2d;
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.toolbar {
  display: flex;
  gap: 0.5rem;
}

.button {
  padding: 0.5rem 1rem;
  border: 1px solid;
  border-radius: 6px;
  background: none;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.light .button {
  border-color: #d0d7de;
  color: #24292f;
}

.light .button:hover {
  background-color: #f3f4f6;
  border-color: #8c959f;
}

.dark .button {
  border-color: #30363d;
  color: #f0f6fc;
}

.dark .button:hover {
  background-color: #21262d;
  border-color: #8b949e;
}

.main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editorPanel,
.previewPanel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editorPanel {
  border-right: 1px solid;
}

.light .editorPanel {
  border-right-color: #e0e0e0;
}

.dark .editorPanel {
  border-right-color: #333333;
}

/* Responsive design */
@media (max-width: 768px) {
  .main {
    flex-direction: column;
  }
  
  .editorPanel {
    border-right: none;
    border-bottom: 1px solid;
  }
  
  .light .editorPanel {
    border-bottom-color: #e0e0e0;
  }
  
  .dark .editorPanel {
    border-bottom-color: #333333;
  }
  
  .header {
    padding: 0.75rem 1rem;
  }
  
  .header h1 {
    font-size: 1.25rem;
  }
  
  .toolbar {
    gap: 0.25rem;
  }
  
  .button {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }
}
